import RoomAvailabilityModel from '../models/unit-availability.model';
import ReservationItemModel from '../models/reservation-item.model';
import { IUnitType, PaymentStatusEnum, ReservationStatusEnum } from '../types';

export interface AvailabilityCheckParams {
  propertyId: string;
  unitTypes: IUnitType[];
  startDateTime: string;
  endDateTime: string;
}

export interface RoomTypeAvailability {
  unitTypeId: string;
  availableRooms: number;
  totalRooms: number;
}

async function getReservations(propertyId: string, unitTypeIds: string[], startDateTime: string, endDateTime: string) {
  return ReservationItemModel.find({
    propertyId,
    unitTypeId: { $in: unitTypeIds },
    startDateTime: { $gte: startDateTime },
    endDateTime: { $lte: endDateTime },
    status: ReservationStatusEnum.CONFIRMED,
    paymentStatus: PaymentStatusEnum.PAID,
  }).lean();
}

async function getRoomAvailabilities(
  propertyId: string,
  unitTypeIds: string[],
  startDateTime: string,
  endDateTime: string,
) {
  const startDate = new Date(startDateTime);
  const endDate = new Date(endDateTime);

  // Get day names for the date range
  const dayNames = [];
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    if (!dayNames.includes(dayName)) {
      dayNames.push(dayName);
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return RoomAvailabilityModel.find({
    propertyId,
    unitTypeId: { $in: unitTypeIds },
    isActive: true,
    fromDate: { $lte: endDate },
    toDate: { $gte: startDate },
    $or: [
      { days: 'all' },
      { days: { $in: dayNames } }
    ]
  }).lean();
}

function generateStayDates(startDateTime: string, endDateTime: string): string[] {
  const stayDates: string[] = [];
  const start = new Date(startDateTime);
  const end = new Date(endDateTime);
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);
  for (let d = new Date(start); d <= end; d.setUTCDate(d.getUTCDate() + 1)) {
    stayDates.push(d.toISOString().split('T')[0]);
  }
  return stayDates;
}

export async function checkRoomAvailability(params: AvailabilityCheckParams): Promise<RoomTypeAvailability[]> {
  const { propertyId, unitTypes, startDateTime, endDateTime } = params;

  // Input validation
  if (!propertyId || !unitTypes?.length || !startDateTime || !endDateTime) {
    throw new Error('Missing required parameters');
  }
  if (new Date(startDateTime) >= new Date(endDateTime)) {
    throw new Error('Start date must be before end date');
  }

  const formattedStartDateTime = new Date(startDateTime).toISOString();
  const formattedEndDateTime = new Date(endDateTime).toISOString();
  const unitTypeIds = unitTypes.map((rt) => rt._id.toString());

  // Fetch data concurrently
  const [reservations, roomAvailabilities, stayDates] = await Promise.all([
    getReservations(propertyId, unitTypeIds, formattedStartDateTime, formattedEndDateTime),
    getRoomAvailabilities(propertyId, unitTypeIds, formattedStartDateTime, formattedEndDateTime),
    Promise.resolve(generateStayDates(startDateTime, endDateTime)),
  ]);

  // Process reservations
  const reservationMap = new Map<string, number>();
  reservations.forEach((reservation) => {
    const rtId = reservation.unitTypeId.toString();
    reservationMap.set(rtId, (reservationMap.get(rtId) || 0) + 1);
  });

  // Process availabilities - find minimum available quantity for each unit type
  const availMap = new Map<string, number>();
  roomAvailabilities.forEach((avail) => {
    const rtId = avail.unitTypeId.toString();
    const currentMin = availMap.get(rtId) ?? Infinity;
    availMap.set(rtId, Math.min(currentMin, avail.quantityAvailable));
  });

  // Calculate availability
  return unitTypes.map((unitType) => {
    const rtId = unitType._id.toString();
    const totalRooms = unitType.totalUnits;
    const reservedCount = reservationMap.get(rtId) || 0;
    const minAvailability = availMap.get(rtId) ?? totalRooms;
    const availableRooms = Math.max(0, minAvailability - reservedCount);

    return {
      unitTypeId: rtId,
      availableRooms,
      totalRooms,
    };
  });
}
